package com.ecco.finance.webApi.dto;

import static com.ecco.infrastructure.util.EccoTimeUtils.LONDON;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dom.contracts.RateCard;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.contracts.ratecards.RateCardCalculationBuildings;
import com.google.common.collect.Range;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class FinanceChargeCalculation {

    private final FixedContainerRepository buildingRepository;

    public enum ChargeReason {MOVE_IN, MOVE_OUT, RATE, REPORT_END}

    public record ChargeChange(
            Instant date,
            ChargeReason reason,
            String description,
            Integer serviceRecipientId, // charge against, eg client
            Integer buildingId,
            RateCard rateCard
    ) {
        public static ChargeChange buildCharge(@NonNull Instant date, @NonNull ChargeReason reason, @NonNull String description, @NonNull Integer srId, @NonNull Integer bldgId) {
            return new ChargeChange(date, reason, description, srId, bldgId, null);
        }
        private static ChargeChange buildRateCard(@NonNull Instant date, @NonNull String description, @NonNull RateCard rateCard) {
            return new ChargeChange(date, ChargeReason.RATE, description, null, null, rateCard);
        }
        private static ChargeChange buildReportEnd(@NonNull Instant date) {
            return new ChargeChange(date, ChargeReason.REPORT_END, null, null, null, null);
        }
    }

    /**
     * Calculate the lines of the charges.
     * Starts from the first chargeable point (which is assumed to be data loaded around the from date)
     * and ends at the report end date (otherwise there might be no end - could possibly do financial year etc)
     * NB chargeChanges can be:
     *  - a set of unrelated charges (eg address history last month)
     *  - a set of charges from one client
     *  - a set of charges from one building
     * Notably, this doesn't have a running total - and we should never with paged address history
     * Notably, each paged address history contains everything for the calculations (so state between pages isn't needed)
     *   - because each item has a start/end which is split into complete set of charges until report end
     *   - and we don't do a running total
     * Notably, each line of address history will have consecutive charge lines which contains everything for the calculations
     *   - so won't have other clients in it
     *   - if another's client data could interject this, then we may need to reconsider this
     */
    public List<ClientSalesChargeInvoiceDetailResource.Line> calculateLines(Range<Instant> reportRange, List<FinanceChargeCalculation.ChargeChange> chargeChanges, List<RateCard> rateCards, boolean isClientBased) {

        // with the data consistency in the chargeChanges, we don't need to verify the data
        // and our report for any month should be consistent (except a total)
        /*if (isClientBased) {
            // for client 'charges'...
            //  - currentRateCard is global to all clients
            //  - currentBldgId is dependent on the move in
            //  - currentSrId is dependent on the move in/out
            // assert that the chargeChanges have only one serviceRecipientId
            // because if client A moves in to currentBldgId then the rate changes then client B moves out we're left with no charges for client A
            Assert.state(chargeChanges.stream().map(ChargeChange::serviceRecipientId).filter(Objects::nonNull).distinct().count() <= 1,
                    () -> "chargeChanges must have at most one serviceRecipientId");

        } else {
            // for building 'charges'...
            // assert that the chargeChanges have only one buildingId
            //  - currentRateCard is global to all clients
            //  - currentBldgId is global to all clients (in a report for a building)
            //  - currentSrId is fine because only one client is assumed at the property at a time
            //      the consequence of this is that the move in/out dates will not apply accurately to clients in the building 'charges' report
            //      to support simultaneous clients (with different move in/out dates), we'd need a currentSrId per srId and an extra charge line
            Assert.state(chargeChanges.stream().map(ChargeChange::buildingId).filter(Objects::nonNull).distinct().count() <= 1,
                    () -> "chargeChanges must have at most one buildingId");
        }*/

        assert reportRange.hasUpperBound(); // enforcing a report range makes logic easier
        // get all rate cards applicable for this period (including rate cards that follow on)
        // enforcing the calling method provides rate cards of the same type
        Assert.state(rateCards.stream().map(RateCard::getChargeNameId).distinct().count() == 1, () -> "rateCards in one calculation must be of the same charge name");

        Integer currentBldgId = null;
        RateCard currentRateCard = null;
        Integer currentSrId = null;
        final RateCardCalculation calculator = new RateCardCalculationBuildings();

        // multiple rate cards are sequential in time (of the same calculation/chargeName)
        var rateCardsInRange = calculator.getRateCardsInDateRange(reportRange, rateCards);
        var chargeChangeRateCards = rateCardsInRange.stream()
                .map(r -> ChargeChange.buildRateCard(r.getStartInstant(), "rate change to id " + r.getChargeNameId(), r))
                .toList();

        // we add the reportRange as a chargeChange item but this does mean it cuts-up the charges exactly to the date
        // however, we only add the end date because that is required else things could go to infinity
        // whereas we just include the nearest charge around the report start date without cutting it up
        var chargeChangeReportEnd = ChargeChange.buildReportEnd(reportRange.upperEndpoint());

        // put the charges together in order
        // TODO change this so the order of chargeChanges remains identical (we should ensure each in/out is together)

        var chargeChangeAll = new ArrayList<ChargeChange>();
        chargeChangeAll.addAll(chargeChanges);
        chargeChangeAll.addAll(chargeChangeRateCards);
        // we could be paged, so not at the end yet, but this is only used to stop the calculation
        chargeChangeAll.add(chargeChangeReportEnd);
        chargeChangeAll.sort(Comparator.comparing(ChargeChange::date));

        // process the charges
        List<ClientSalesChargeInvoiceDetailResource.Line> lines = new ArrayList<>();
        var lastIndex = chargeChangeAll.size() - 1;
        for (int i = 0; i < chargeChangeAll.size(); i++) {
            var current = chargeChangeAll.get(i);
            // next is the move out or a new rate card or the report end date
            // in fact, we prioritise the report date if it comes up
            var next = current.reason.equals(ChargeReason.REPORT_END)
                    ? null
                    : (i+1) <= lastIndex
                    ? chargeChangeAll.get(i+1)
                    : null;

            currentSrId = current.reason.equals(ChargeReason.MOVE_IN)
                    ? current.serviceRecipientId
                    : current.reason.equals(ChargeReason.MOVE_OUT)
                    ? null
                    : currentSrId;
            currentRateCard = current.reason.equals(ChargeReason.RATE) ? current.rateCard : currentRateCard;
            currentBldgId = current.reason.equals(ChargeReason.MOVE_IN) ? current.buildingId : currentBldgId;

            // to do a charge, we need to not be at the last item, and have a rate card (which could be really far in the past) and a move in available now
            if (next != null && currentRateCard != null && currentSrId != null) {
                var chargeFrom = current.date;
                var chargeTo = next.date != null ? next.date : null;
                var buildingChargeCategoryInfo = findChargeCategoryForBuilding(currentBldgId, currentRateCard);

                // if there is nothing to charge on the bldg, just skip
                if (buildingChargeCategoryInfo != null) {

                    // TODO: This needs to not make assumptions on what rate card entries match or do conversions
                    // find an entry matching matchChargeCategoryId to the building chargeCategoryId
                    var entries = calculator.determineRateCardEntries(currentRateCard, null, buildingChargeCategoryInfo.getChargeCategoryId(), null);

                    // Create a ZonedDateTime range for the charge period - TODO: Clarify if we want UTC or LONDON as the zone
                    if (chargeTo != null) {
                        ZonedDateTime start = chargeFrom.atZone(LONDON);
                        ZonedDateTime end = chargeTo.atZone(LONDON);
                        Assert.state(!start.isAfter(end), "start must not be after end");
                        Range<ZonedDateTime> dateTimeRange = Range.closedOpen(start, end);
                        var charge = calculator.calculateCharge(entries, dateTimeRange);

                        var line = new ClientSalesChargeInvoiceDetailResource.Line(currentSrId,
                                UUID.randomUUID(), null, current.description, currentRateCard.getId(), currentRateCard.getChargeNameId(), charge, BigDecimal.valueOf(20), null, false,
                                current.buildingId, LocalDateTime.ofInstant(chargeFrom, LONDON), LocalDateTime.ofInstant(chargeTo, LONDON));
                        lines.add(line);
                    }
                }
            }
        }

        return lines;
    }

    public FixedContainer.@Nullable ChargeCategoryCombinations findChargeCategoryForBuilding(Integer buildingId, RateCard rateCard) {
        return buildingRepository.findById(buildingId)
                .map(building -> {
                    // Find a charge category combination that matches the rate card's chargeNameId
                    return findChargeCategoryCombinationForRateCard(building, rateCard);
                })
                .orElse(null);
    }

    /**
     * Finds a charge category combination that matches the given RateCard's chargeNameId.
     * Returns the first matching combination (expected to be one per calc per building), or null if no match is found.
     */
    FixedContainer.@Nullable ChargeCategoryCombinations findChargeCategoryCombinationForRateCard(@NonNull FixedContainer building, @NonNull RateCard rateCard) {
        var combinations = building.getNearestChargeCategoryCombinations();

        for (var combination : combinations) {
            if (combination.getChargeNameId() != null && rateCard.getChargeNameId() != null) {
                if (combination.getChargeNameId().equals(rateCard.getChargeNameId())) {
                    return combination;
                }
            } else if (combination.getChargeNameId() == null && rateCard.getChargeNameId() == null) {
                // Both are null, consider it a match
                return combination;
            }
        }

        return null;
    }

}
