package com.ecco.webApi.finance

import com.ecco.contacts.dao.AddressHistoryRepository
import com.ecco.dom.contacts.AddressHistory
import com.ecco.finance.webApi.dto.ClientSalesChargeInvoiceDetailResource
import com.ecco.infrastructure.util.EccoTimeUtils.LONDON
import com.ecco.repositories.contracts.ContractRepository
import com.google.common.collect.Range
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate

@Service
class FinanceService(
    private val addressHistoryRepository: AddressHistoryRepository,
    private val contractRepository: ContractRepository,
    private val calc: FinanceChargeCalculationDefault,
) {

    companion object {
        const val CHARGE_CONTRACT_ID: Int = 203
    }

    /**
     * Get the charges for a service recipient.
     * @param fromDate inclusive start date, defaults to epoch
     * @param toDate inclusive end date, defaults to end of today
     */
    fun getChargesByServiceRecipient(
        serviceRecipientId: Int,
        fromDate: LocalDate?,
        toDate: LocalDate?,
    ): MutableList<ClientSalesChargeInvoiceDetailResource.Line> {
        // NB we get all the history so we can be sure we have a move in
        val history = addressHistoryRepository.findByServiceRecipientIdOrderByValidFromDesc(
            serviceRecipientId,
        )

        val from = fromDate ?: LocalDate.EPOCH
        val endInstant = toDate.let { it?.plusDays(1) ?: LocalDate.now() }.atStartOfDay(LONDON).toInstant()
        val dates = Range.openClosed(from.atStartOfDay(LONDON).toInstant(), endInstant)

        return this.getCharges(history, dates, true)
    }

    fun getCharges(
        history: MutableList<AddressHistory>,
        dates: Range<Instant>,
        isClientBased: Boolean,
    ): MutableList<ClientSalesChargeInvoiceDetailResource.Line> {
        // get the last contract (for testing) of 'service charge' type (regardless of dates), to use its rate cards
        // NB See RateCardCalculation#getRateCardInDate and DemandSchedule#allowableRateCards for how we can choose rateCards for rotas
        val contract = contractRepository.findByContractTypeIdOrderByIdDesc(
            CHARGE_CONTRACT_ID,
        )
            .stream().findFirst().orElseThrow() // first contract

        return calc.calculateLines(dates, history, contract.getRateCards(), isClientBased)
    }
}